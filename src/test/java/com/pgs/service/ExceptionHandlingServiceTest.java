package com.pgs.service;

import com.pgs.dto.exception.MatchTransactionRequest;
import com.pgs.entity.*;
import com.pgs.enums.*;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExceptionHandlingServiceTest {

    @Mock
    private DepositRequestRepository depositRequestRepository;
    
    @Mock
    private WithdrawalRequestRepository withdrawalRequestRepository;
    
    @Mock
    private BankTransactionRepository bankTransactionRepository;
    
    @Mock
    private BankAccountRepository bankAccountRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private AuditService auditService;
    
    @Mock
    private DepositService depositService;
    
    @Mock
    private WithdrawalService withdrawalService;

    @InjectMocks
    private ExceptionHandlingService exceptionHandlingService;

    private BankTransaction transaction;
    private DepositRequest depositRequest;
    private WithdrawalRequest withdrawalRequest;
    private BankAccount bankAccount;
    private MatchTransactionRequest matchRequest;

    @BeforeEach
    void setUp() {
        // Setup bank account
        bankAccount = BankAccount.builder()
            .id(UUID.randomUUID())
            .nickname("Test Account")
            .accountNumber("**********")
            .bankName("Test Bank")
            .currentBalance(new BigDecimal("1000.00"))
            .dailyInUsed(BigDecimal.ZERO)
            .dailyOutUsed(BigDecimal.ZERO)
            .freezedDailyInUsed(new BigDecimal("100.00"))
            .freezedDailyOutUsed(new BigDecimal("50.00"))
            .totalIn(BigDecimal.ZERO)
            .totalOut(BigDecimal.ZERO)
            .build();

        // Setup transaction
        transaction = BankTransaction.builder()
            .id(UUID.randomUUID())
            .bankAccount(bankAccount)
            .amount(new BigDecimal("100.00"))
            .direction(TransactionDirection.IN)
            .status(TransactionStatus.PENDING)
            .build();

        // Setup deposit request
        depositRequest = DepositRequest.builder()
            .id(UUID.randomUUID())
            .amount(new BigDecimal("100.00"))
            .status(DepositStatus.PENDING)
            .assignedBankAccount(bankAccount)
            .build();

        // Setup withdrawal request
        withdrawalRequest = WithdrawalRequest.builder()
            .id(UUID.randomUUID())
            .amount(new BigDecimal("50.00"))
            .status(WithdrawalStatus.PENDING)
            .bankAccount(bankAccount)
            .build();

        // Setup match request
        matchRequest = new MatchTransactionRequest();
        matchRequest.setTransactionId(transaction.getId().toString());
        matchRequest.setRequestId(depositRequest.getId().toString());
    }

    @Test
    void testMatchDepositTransactionToRequest_Success() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act
        exceptionHandlingService.matchDepositTransactionToRequest(matchRequest);

        // Assert
        assertEquals(depositRequest.getId(), transaction.getMatchedRequestId());
        assertEquals(TransactionStatus.COMPLETED, transaction.getStatus());
        assertEquals(transaction, depositRequest.getMatchedTransaction());
        assertEquals(DepositStatus.MATCHED, depositRequest.getStatus());
        
        verify(bankTransactionRepository).save(transaction);
        verify(depositRequestRepository).save(depositRequest);
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_MATCH"), eq("TRANSACTION"), eq(transaction.getId()), any());
    }

    @Test
    void testMatchWithdrawalTransactionToRequest_Success() {
        // Arrange
        transaction.setDirection(TransactionDirection.OUT); // Make it outgoing for withdrawal
        matchRequest.setRequestId(withdrawalRequest.getId().toString());
        
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(withdrawalRequestRepository.findById(withdrawalRequest.getId())).thenReturn(Optional.of(withdrawalRequest));

        // Act
        exceptionHandlingService.matchWithdrawalTransactionToRequest(matchRequest);

        // Assert
        assertEquals(withdrawalRequest.getId(), transaction.getMatchedRequestId());
        assertEquals(TransactionStatus.COMPLETED, transaction.getStatus());
        assertEquals(transaction, withdrawalRequest.getTransaction());
        assertEquals(WithdrawalStatus.SUCCESS, withdrawalRequest.getStatus());
        
        verify(bankTransactionRepository).save(transaction);
        verify(withdrawalRequestRepository).save(withdrawalRequest);
        verify(bankAccountRepository).save(bankAccount);
        verify(auditService).logUserAction(any(), eq("MANUAL_MATCH"), eq("TRANSACTION"), eq(transaction.getId()), any());
    }

    @Test
    void testMatchDepositTransactionToRequest_TransactionNotFound() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_DepositRequestNotFound() {
        // Arrange
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_TransactionAlreadyMatched() {
        // Arrange
        transaction.setMatchedRequestId(UUID.randomUUID());
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchDepositTransactionToRequest_OutgoingTransactionNotAllowed() {
        // Arrange
        transaction.setDirection(TransactionDirection.OUT);
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(depositRequestRepository.findById(depositRequest.getId())).thenReturn(Optional.of(depositRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () -> 
            exceptionHandlingService.matchDepositTransactionToRequest(matchRequest));
    }

    @Test
    void testMatchWithdrawalTransactionToRequest_IncomingTransactionNotAllowed() {
        // Arrange
        matchRequest.setRequestId(withdrawalRequest.getId().toString());
        when(bankTransactionRepository.findById(transaction.getId())).thenReturn(Optional.of(transaction));
        when(withdrawalRequestRepository.findById(withdrawalRequest.getId())).thenReturn(Optional.of(withdrawalRequest));

        // Act & Assert
        assertThrows(BadRequestException.class, () -> 
            exceptionHandlingService.matchWithdrawalTransactionToRequest(matchRequest));
    }
}
